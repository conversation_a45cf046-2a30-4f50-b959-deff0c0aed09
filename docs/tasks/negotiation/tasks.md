# Rate Negotiation Implementation Tasks

## Overview

Implementation of rate negotiation system using match-centric workflow with MatchStep metadata for state management. This approach leverages existing infrastructure while avoiding complex new database models.

---

## Database Schema Updates

### 1. Add JobCompensation Model

**File:** `packages/db-medical/prisma/schema.prisma`

Create new model to track compensation details and negotiation state:

- `id`, `matchId`, `jobId` as primary and foreign keys
- `minRate`, `maxRate`, `currentOfferRate` for rate boundaries
- `finalAgreedRate`, `negotiationStatus` for completion tracking
- `rateStrategy`, `negotiationCount` for workflow management
- Relationships to `Match` and `JobPost` models

### 2. Extend JobPost Model

**File:** `packages/db-medical/prisma/schema.prisma`

Add rate negotiation fields to existing JobPost:

- `minNegotiableRate` and `maxNegotiableRate` for boundaries
- `finalNegotiatedRate` for storing agreed rate
- `allowRateNegotiation` boolean flag

### 3. Update Action Constants

**File:** `packages/api-medical/src/constants/actions.ts`

Add new action types for rate negotiation events:

- `START_RATE_NEGOTIATION` - Initialize negotiation step
- `SUBMIT_RATE_OFFER` - New offer submitted
- `ACCEPT_RATE_OFFER` - Offer accepted
- `DECLINE_RATE_NEGOTIATION` - Offer declined
- `FINALIZE_RATE_NEGOTIATION` - Final agreement reached

### 4. Database Migration

**File:** `packages/db-medical/prisma/migrations/`

Generate and apply Prisma migration for:

- New JobCompensation model
- JobPost schema extensions
- Updated model relationships

---

## Core API Implementation

### 5. Matches Router - CRUD Operations

**File:** `packages/api-medical/src/router/jobs/matches/matches.ts`

Implement standard tRPC procedures:

- `create` - Create new match between provider and organization
- `get` - Retrieve single match with includes for job, steps, compensation
- `list` - List matches with filtering by status, user, organization
- `update` - Update match status and metadata
- `delete` - Soft delete match (update status to cancelled)

Include proper authorization checks and input validation using Zod schemas.

### 6. Match Steps Router - Lifecycle Management

**File:** `packages/api-medical/src/router/jobs/matches/steps.ts`

Handle MatchStep progression and metadata:

- `list` - Get all steps for a match with completion status
- `get` - Retrieve specific step with metadata
- `start` - Initialize new step (creates MatchStep record)
- `complete` - Mark step complete with success/failure status
- `updateMetadata` - Update step-specific JSON metadata

Ensure steps follow proper sequence and dependency validation.

### 7. Rate Negotiation Router - Specialized Operations

**File:** `packages/api-medical/src/router/jobs/matches/rates.ts`

Rate-specific procedures that manage MatchStep metadata:

- `start` - Initialize rate negotiation step with JobCompensation
- `submitOffer` - Submit new rate offer (updates metadata and JobCompensation)
- `respondToOffer` - Accept/decline/counter existing offer
- `finalize` - Complete negotiation with agreed rate
- `getHistory` - Retrieve full negotiation timeline from metadata

All operations update both MatchStep.metadata and JobCompensation records.

### 8. Rate Validation and Helpers

**File:** `packages/api-medical/src/lib/matches/helpers.ts`

Shared validation and utility functions:

- Rate boundary validation against job min/max
- Permission checks for offer submission/response
- Negotiation state validation (rounds, expiration)
- Metadata parsing and updating utilities
- Rate strategy logic (conservative, competitive, etc.)

### 9. Offer Workflow Logic

**File:** `packages/api-medical/src/lib/matches/workflows.ts`

Business logic for offer lifecycle:

- Accept offer workflow (update compensation, complete step)
- Decline offer workflow (record reason, update metadata)
- Counter offer workflow (validate, create new offer)
- Finalization workflow (create contract, update job)
- Expiration handling (auto-decline expired offers)

### 10. Router Registration

**Files:**

- `packages/api-medical/src/router/jobs/matches/index.ts`
- `packages/api-medical/src/router/index.ts`

Export matches router and register in main API:

- Combine all match-related routers (matches, steps, rates, etc.)
- Register with proper middleware and authentication
- Ensure consistent error handling and logging

---

## System Integration

### 11. Job Posting Extensions

**File:** `packages/api-medical/src/router/jobs/jobs.ts`

Extend existing job operations:

- Include rate negotiation fields in job queries
- Add mutations for setting rate boundaries
- Update job creation to include negotiation settings
- Link jobs to match creation workflow

### 12. Message System Integration

**File:** `packages/api-medical/src/router/messages/messages.ts`

Enhance existing message functionality:

- Extend message types for rate negotiation events
- Link messages to match-based conversations (already supported)
- Add structured message templates for offers/responses
- Include rate details in message metadata

### 13. Action Handlers and Notifications

**Files:**

- `packages/api-medical/src/lib/actions/handlers/rateNegotiation.ts`
- `packages/api-medical/src/lib/actions/index.ts`

Implement notification logic:

- Handle rate negotiation action types
- Send notifications to providers and organizations
- Create audit trails for all negotiation events
- Register handlers in existing action dispatcher system

### 14. Contract Creation Integration

**File:** `packages/api-medical/src/router/jobs/matches/contracts.ts`

Match-based contract creation:

- `createFromMatch` procedure to generate contracts from finalized matches
- Use JobCompensation.finalAgreedRate for contract rate
- Link contract to match for tracking
- Integrate with existing contract workflow

---

## Advanced Features

### 15. Offer Expiration System

**File:** `packages/api-medical/src/jobs/matches/expiration.ts`

Background job implementation:

- Schedule offer expiration based on metadata
- Auto-update expired offers in MatchStep metadata
- Send expiration notifications
- Clean up expired negotiation states

### 16. Admin Dashboard

**File:** `packages/api-medical/src/router/jobs/matches/admin.ts`

Administrative controls:

- List negotiations with filtering and search
- View negotiation statistics and analytics
- System configuration for expiration times and limits
- Override capabilities for stuck negotiations

### 17. Timeline and History

**File:** `packages/api-medical/src/router/jobs/matches/timeline.ts`

Unified timeline view:

- Merge actions, offers, and messages into single timeline
- Filter by event type, participant, date range
- Search negotiation events and messages
- Export negotiation history for reporting

---

## Summary

**Total Tasks:** 17 focused tasks  
**Architecture:** Match-centric with MatchStep metadata state management  
**Database Changes:** Minimal - JobCompensation model + JobPost extensions  
**Integration:** Leverage existing message, action, and contract systems

This approach reduces complexity by 90% while maintaining full negotiation functionality through smart reuse of existing infrastructure and JSON metadata for application-level state management.
