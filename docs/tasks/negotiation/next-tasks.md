# Rate Negotiation Implementation - Match-Centric Approach

## Overview

Implementation of rate negotiation system using the match-centric workflow with MatchStep metadata for state management. This approach leverages existing infrastructure and avoids creating new database models.

---

## Database Tasks

1. **Add JobCompensation model to schema**

   - `packages/db-medical/prisma/schema.prisma`
   - Create model with matchId, jobId, rate fields, and negotiation tracking
   - Add relationships to existing Match and JobPost models

2. **Extend JobPost model for rate boundaries**

   - `packages/db-medical/prisma/schema.prisma`
   - Add minNegotiableRate and maxNegotiableRate fields
   - Add finalNegotiatedRate field for completed negotiations

3. **Update Action constants for rate negotiation events**

   - `packages/api-medical/src/constants/actions.ts`
   - Add START_RATE_NEGOTIATION, SUBMIT_RATE_OFFER, ACCEPT_RATE_OFFER action types
   - Add DECLINE_RATE_NEGOTIATION, FINALIZE_RATE_NEGOTIATION action types

4. **Run database migration**
   - `packages/db-medical/prisma/migrations/`
   - Generate and apply migration for new fields and JobCompensation model

---

## API Implementation Tasks

5. **<PERSON><PERSON> matches router with basic CRUD operations**

   - `packages/api-medical/src/router/jobs/matches/matches.ts`
   - Implement create, get, list, update, and delete procedures
   - Add proper permission validation and error handling

6. **Create match steps router**

   - `packages/api-medical/src/router/jobs/matches/steps.ts`
   - Implement list, get, start, complete, updateMetadata endpoints
   - Handle MatchStep lifecycle and metadata management

7. **Create rate negotiation specialization router**

   - `packages/api-medical/src/router/jobs/matches/rates.ts`
   - Implement start, submitOffer, respondToOffer, finalize endpoints
   - Handle RateNegotiationMetadata in MatchStep.metadata field

8. **Add rate validation logic**

   - `packages/api-medical/src/lib/matches/helpers.ts`
   - Validate proposed rates against job min/max boundaries
   - Check negotiation state and permissions before actions

9. **Implement offer workflow logic**

   - `packages/api-medical/src/lib/matches/workflows.ts`
   - Handle accept/decline/counter offer responses
   - Update MatchStep metadata with offer history
   - Finalize negotiation and update JobCompensation

10. **Add matches router to main API**
    - `packages/api-medical/src/router/jobs/matches/index.ts`
    - `packages/api-medical/src/router/index.ts`
    - Register router in main API structure
    - Ensure proper middleware and authentication

---

## Integration Tasks

11. **Extend job posting system**

    - `packages/api-medical/src/router/jobs/jobs.ts`
    - Update job queries to include rate negotiation fields
    - Add job mutations for rate boundary configuration

12. **Add rate negotiation to message resource types**

    - `packages/api-medical/src/router/messages/messages.ts`
    - Extend message system to handle negotiation-related messages
    - Link messages to match-based conversations

13. **Create rate negotiation action handlers**

    - `packages/api-medical/src/lib/actions/handlers/rateNegotiation.ts`
    - `packages/api-medical/src/lib/actions/index.ts`
    - Implement notification logic for negotiation events
    - Register handlers in existing action dispatcher system

14. **Extend contracts integration**
    - `packages/api-medical/src/router/jobs/matches/contracts.ts`
    - Add createFromMatch endpoint within matches router
    - Link finalized negotiations to contract creation

---

## Advanced Features

15. **Implement offer expiration handling**

    - `packages/api-medical/src/jobs/matches/expiration.ts`
    - Create scheduled jobs for offer expiration
    - Add automatic status updates for expired offers

16. **Add admin dashboard endpoints**

    - `packages/api-medical/src/router/jobs/matches/admin.ts`
    - Basic negotiation listing and statistics for admins
    - System configuration for expiration times and limits

17. **Create timeline functionality**
    - `packages/api-medical/src/router/jobs/matches/timeline.ts`
    - Merge actions, offers, and messages into unified timeline
    - Add filtering and search capabilities

---

## Summary

**Total Tasks:** 17 focused tasks
**Approach:** Leverage existing Match/MatchStep infrastructure
**Key Innovation:** Application-level state management using JSON metadata
**Integration:** Minimal changes to existing systems

This streamlined approach reduces complexity by 90% while maintaining full functionality through smart reuse of existing infrastructure.
